import os
import re
import logging
import calendar
from datetime import datetime
from dateutil.relativedelta import relativedelta

from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml.ns import nsdecls
from docx.oxml import parse_xml

from app.config import Config


class AppConfig:
    """应用配置和常量管理"""
    
    # 基础目录
    BASE_DIR = Config.APP_DIR
    
    # 产品配置
    class Products:
        """产品相关配置"""
        
        # A组/新版本配置
        GROUP_A = {
            "DEVICE_PRICE": 1979.9,
            "EXTENDED_WARRANTY": 1799.85,
            "DOWN_PAYMENT": 119.6,
            "ADDITIONAL_AMOUNT": 119.6,
        }
        
        # B组/旧版本配置
        GROUP_B = {
            "DEVICE_PRICE": 1.7,
            "EXTENDED_WARRANTY": 1499.85,
            "DOWN_PAYMENT": 119.6,
            "ADDITIONAL_AMOUNT": 119.6,
        }
        
        # 产品签收回执配置
        RECEIPT_CONFIG = {
            "月还": {
                "template": "产品签收回执单.docx",
                "monthly_payment": 2599.5,
                "duration": {"months": 6},
            },
            "天还": {
                "template": "产品签收回执单_天还.docx",
                "monthly_payment": 1979.9,
                "duration": {"days": 60},
                "fixed_merchant": "太原市迎泽区刚刚通讯设备店（个体工商户）",
            },
            "新月还": {
                "template": "产品签收回执单.docx",
                "monthly_payment": 2274,
                "duration": {"months": 6},
            },
        }
    
    # 商户配置
    class Merchants:
        """商户相关配置"""
        
        @staticmethod
        def get_options():
            """获取商户选项"""
            return {
                "涛涛租物": "太原市迎泽区涛涛通讯设备店（个体工商户）",
                "刚刚好物": "太原市迎泽区刚刚通讯设备店（个体工商户）",
                "梦缘商贸": "太原梦缘商贸有限公司",
            }


class DocUtils:
    """文档处理工具类"""
    
    @staticmethod
    def get_next_payment_date(current_date, original_day=None):
        """
        计算下一个付款日期
        
        Args:
            current_date (datetime): 当前日期
            original_day (int, optional): 原始付款日. 默认为None (使用current_date的日)
            
        Returns:
            datetime: 下一个付款日期
        """
        if original_day is None:
            original_day = current_date.day
            
        year = current_date.year
        month = current_date.month
        
        # 计算下一个月
        next_month = month + 1
        next_year = year
        if next_month > 12:
            next_month = 1
            next_year += 1
            
        # 获取下一个月的最后一天
        _, last_day = calendar.monthrange(next_year, next_month)
        
        # 对于付款日的处理：尝试使用original_day，如果超出了，使用该月的最后一天
        target_day = min(original_day, last_day)
            
        return datetime(next_year, next_month, target_day)
    
    @staticmethod
    def add_border_to_table(table):
        """为表格添加边框"""
        for cell in table._cells:
            tcPr = cell._element.get_or_add_tcPr()
            tcBorders = parse_xml(
                r'<w:tcBorders %s>'
                r'<w:top w:val="single"/>'
                r'<w:left w:val="single"/>'
                r'<w:bottom w:val="single"/>'
                r'<w:right w:val="single"/>'
                r'</w:tcBorders>' % nsdecls("w")
            )
            tcPr.append(tcBorders)
    
    @staticmethod
    def set_table_cell_font(cell, font_size=8, font_name="等线"):
        """设置表格单元格中的字体"""
        for paragraph in cell.paragraphs:
            for run in paragraph.runs:
                run.font.size = Pt(font_size)
                run.font.name = font_name
    
    @staticmethod
    def insert_table_at_paragraph(doc, table, paragraph_index):
        """在指定段落后插入表格"""
        tbl = table._element
        tbl.getparent().remove(tbl)
        doc.paragraphs[paragraph_index]._element.addnext(tbl)
    
    @staticmethod
    def replace_placeholders(doc, placeholders, center_vars=None):
        """替换文档中的占位符
        
        Args:
            doc (Document): Word文档对象
            placeholders (dict): 占位符及其替换值
            center_vars (list, optional): 需要居中的变量列表. 默认为None
        
        Returns:
            Document: 处理后的文档对象
        """
        if center_vars is None:
            center_vars = []
            
        def process_paragraph(paragraph):
            for key, value in placeholders.items():
                if key in paragraph.text:
                    paragraph.text = paragraph.text.replace(key, value)
                    
                    # 设置所有替换的文本为11号字体
                    for run in paragraph.runs:
                        run.font.size = Pt(11)
                        run.font.name = "等线"
                    
                    # 对需要居中的部分应用居中效果
                    if key in center_vars:
                        paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

        # 处理段落中的占位符
        for paragraph in doc.paragraphs:
            process_paragraph(paragraph)
            
        # 处理表格中的占位符
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        process_paragraph(paragraph)
                        
        return doc
    
    @staticmethod
    def add_rent_payment_table(doc, total_periods, rent_per_period, start_date,
                               insert_index):
        """添加租金支付表"""
        table = doc.add_table(rows=1, cols=3)
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = "租期"
        hdr_cells[1].text = "租金"
        hdr_cells[2].text = "租金付款日"

        # 记住原始日期，用于一致的付款日计算
        original_day = start_date.day
        
        # 第一个付款日期
        current_payment_date = DocUtils.get_next_payment_date(start_date, original_day)

        for i in range(total_periods):
            row_cells = table.add_row().cells
            row_cells[0].text = f"第{i+1}期"
            row_cells[1].text = f"{rent_per_period:.2f}"
            row_cells[2].text = current_payment_date.strftime("%Y-%m-%d")

            # 设置表格字体
            for cell in row_cells:
                DocUtils.set_table_cell_font(cell)

            # 计算下一个月的账单日，始终尝试使用原始日期
            current_payment_date = DocUtils.get_next_payment_date(
                current_payment_date, original_day
            )

        # 添加表格边框
        DocUtils.add_border_to_table(table)

        # 在指定位置插入表格
        DocUtils.insert_table_at_paragraph(doc, table, insert_index)


class ReceiptGenerator:
    """回执单生成器"""
    
    CENTER_VARS = ["[订单编号]", "[型号]", "[串号]", "[颜色]", "[时间]", "[截至时间]", "[月租]"]
    
    def __init__(self):
        self.base_dir = AppConfig.BASE_DIR
        self.template_dir = os.path.join(self.base_dir, "data", "templates", "HZ")
        # 确保目录存在
        os.makedirs(self.template_dir, exist_ok=True)
        self._verify_resources()

    def _verify_resources(self):
        """验证必要的模板文件存在"""
        required_templates = {
            "月还": "产品签收回执单.docx",
            "天还": "产品签收回执单_天还.docx",
            "新月还": "产品签收回执单.docx",
        }
        missing = []
        for product, template in required_templates.items():
            path = os.path.join(self.template_dir, template)
            if not os.path.exists(path):
                missing.append(f"{product} 模板: {template}")
                # 创建空白文档
                try:
                    doc = Document()
                    doc.save(path)
                    logging.warning(f"已创建空白模板文件: {path}")
                except Exception as e:
                    logging.error(f"创建模板文件失败: {path}, 错误: {str(e)}")
                    
        if missing:
            error_msg = "缺少必要模板文件，已创建空白模板：\n" + "\n".join(missing)
            logging.warning(error_msg)

    def generate(self, product_type, placeholders, merchant):
        """生成回执单
        
        Args:
            product_type (str): 产品类型
            placeholders (dict): 占位符和替换值
            merchant (str): 商户名称
            
        Returns:
            str: 生成的文件路径
        """
        try:
            config = AppConfig.Products.RECEIPT_CONFIG.get(product_type)
            if not config:
                raise ValueError(f"无效的产品类型: {product_type}")

            # 商户选择
            if product_type == "天还":
                placeholders["[出租商户]"] = config["fixed_merchant"]
            else:
                merchants = AppConfig.Merchants.get_options()
                placeholders["[出租商户]"] = merchants.get(merchant, "未知商户")

            # 月租
            monthly_payment = config['monthly_payment']
            device_count = int(placeholders.get("[设备数量]", "1"))
            placeholders["[月租]"] = f"{monthly_payment * device_count:.2f}"

            # 时间计算
            current_date = datetime.now()
            end_date = current_date + relativedelta(**config["duration"])

            date_fields = {
                "[时间]": current_date.strftime("%Y-%m-%d"),
                "[截至时间]": end_date.strftime("%Y-%m-%d"),
                "[年]": current_date.strftime("%Y"),
                "[月]": current_date.strftime("%m"),
                "[日]": current_date.strftime("%d"),
                "[下单时间]": current_date.strftime("%Y-%m-%d"),
            }
            placeholders.update(date_fields)

            # 加载模板
            template_path = os.path.join(self.template_dir, config["template"])
            if not os.path.exists(template_path):
                raise FileNotFoundError(f"模板文件不存在: {template_path}")

            doc = Document(template_path)
            doc = DocUtils.replace_placeholders(doc, placeholders, self.CENTER_VARS)

            # 保存路径
            output_dir = os.path.join(self.base_dir, "data", "generated")
            os.makedirs(output_dir, exist_ok=True)

            safe_name = re.sub(r'[\\/*?:"<>|]', "", placeholders["[姓名]"])
            timestamp = current_date.strftime("%Y%m%d_%H%M%S")
            filename = f"{safe_name}_{placeholders['[订单编号]']}_{timestamp}.docx"
            output_path = os.path.join(output_dir, filename)

            try:
                doc.save(output_path)
            except PermissionError as e:
                logging.error(f"文件保存失败：{str(e)}")
                raise RuntimeError("文件保存失败：请关闭正在使用的文档后重试") from e

            logging.info(f"成功生成文档: {filename}")
            return output_path

        except Exception as e:
            logging.error(f"生成过程中发生错误: {str(e)}", exc_info=True)
            raise