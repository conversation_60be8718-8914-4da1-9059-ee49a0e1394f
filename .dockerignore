# Git files
.git/
.gitignore
.gitattributes

# Python cache and compiled files
__pycache__/
*.py[cod]
*.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Docker files (build时不需要包含)
Dockerfile*
docker-compose.yml
.dockerignore

# Documentation and markdown
*.md
docs/
README*

# Test files
tests/
test_*
*_test.py
.pytest_cache/
.coverage
htmlcov/
.tox/

# Logs and temporary files
logs/
*.log
cache/
tmp/
temp/
.tmp/

# Output directories
output/

# OS generated files
Thumbs.db
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# Backup files
*.bak
*.backup
*.old

# Configuration files that might contain secrets
.env
.env.local
.env.*.local

# Application specific ignores
client_side_cert.pem
